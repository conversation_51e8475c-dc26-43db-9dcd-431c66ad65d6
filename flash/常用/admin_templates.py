# 管理员页面HTML模板

ADMIN_LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>管理员登录</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h1 {
            color: #333;
            margin: 0;
            font-size: 2rem;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .login-btn:hover {
            transform: translateY(-2px);
        }
        .error-message {
            color: #e74c3c;
            text-align: center;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🔐 管理员登录</h1>
        </div>
        <form method="post">
            <div class="form-group">
                <label for="password">管理员密码:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="login-btn">登录</button>
            {% if error %}
            <div class="error-message">{{ error }}</div>
            {% endif %}
        </form>
    </div>
</body>
</html>
'''

ADMIN_DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>管理员控制台</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 { margin: 0; }
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .tab {
            padding: 12px 24px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }
        .tab-content {
            display: none;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .tab-content.active { display: block; }
        .form-group {
            margin-bottom: 20px;
        }
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        .form-row > div {
            flex: 1;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 14px;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn:hover { background: #5a6fd8; }
        .btn-danger { background: #e74c3c; }
        .btn-danger:hover { background: #c0392b; }
        .config-list {
            margin-top: 20px;
        }
        .config-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        .config-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .config-item p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .stage-input {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }
        .stage-input input {
            width: 80px;
        }
        .stage-input button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
        }
        #stages-container .stage-input:first-child button {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛠️ 管理员控制台</h1>
        <a href="/admin/logout" class="logout-btn">退出登录</a>
    </div>

    {% if message %}
    <div class="success-message">{{ message }}</div>
    {% endif %}
    {% if error %}
    <div class="error-message">{{ error }}</div>
    {% endif %}

    <div class="tabs">
        <button class="tab active" onclick="showTab('files')">文件配置</button>
        <button class="tab" onclick="showTab('urls')">URL管理</button>
        <button class="tab" onclick="showTab('settings')">全局设置</button>
    </div>

    <!-- 文件配置标签页 -->
    <div id="files" class="tab-content active">
        <h2>📁 文件配置管理</h2>
        <form method="post" action="/admin/save_file_config">
            <div class="form-row">
                <div>
                    <label for="file_name">选择文件:</label>
                    <select name="file_name" id="file_name" required onchange="loadFileConfig()">
                        <option value="">请选择文件</option>
                        {% for file in available_files %}
                        <option value="{{ file }}">{{ file }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label for="threshold">阈值:</label>
                    <input type="number" name="threshold" id="threshold" min="1" required>
                </div>
            </div>
            <div class="form-row">
                <div>
                    <label for="highlight_num">高光个数:</label>
                    <input type="number" name="highlight_num" id="highlight_num" min="1" required>
                </div>
                <div>
                    <label for="notification">通知内容:</label>
                    <input type="text" name="notification" id="notification" required>
                </div>
            </div>
            <div class="form-row">
                <div>
                    <label>
                        <input type="checkbox" name="is_ph_file" id="is_ph_file"> PH文件
                    </label>
                </div>
                <div>
                    <label>
                        <input type="checkbox" name="is_ma" id="is_ma"> MA文件
                    </label>
                </div>
            </div>
            <div class="form-group">
                <label>显示阶段:</label>
                <div id="stages-container">
                    <div class="stage-input">
                        <input type="text" name="stage_names[]" placeholder="阶段名称" required>
                        <input type="number" name="stage_starts[]" placeholder="开始" min="1" required>
                        <input type="number" name="stage_ends[]" placeholder="结束(-1表示到末尾)" required>
                        <button type="button" onclick="removeStage(this)">删除</button>
                    </div>
                </div>
                <button type="button" onclick="addStage()">添加阶段</button>
            </div>
            <button type="submit" class="btn">保存配置</button>
        </form>

        <div class="config-list">
            <h3>📋 已配置文件</h3>
            {% for file_name, config in file_configs.items() %}
            <div class="config-item">
                <h4>{{ file_name }}</h4>
                <p><strong>阈值:</strong> {{ config.threshold }} | <strong>高光个数:</strong> {{ config.highlight_num }}</p>
                <p><strong>通知:</strong> {{ config.notification }}</p>
                <p><strong>显示阶段:</strong> 
                {% for stage in config.display_stages %}
                {{ stage.name }}({{ stage.start }}-{{ stage.end }}){% if not loop.last %}, {% endif %}
                {% endfor %}
                </p>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- URL管理标签页 -->
    <div id="urls" class="tab-content">
        <h2>🔗 URL管理</h2>
        <form method="post" action="/admin/save_url_config">
            <div class="form-row">
                <div>
                    <label for="url_path">URL路径:</label>
                    <input type="text" name="url_path" id="url_path" placeholder="/example" required>
                </div>
                <div>
                    <label for="url_files">关联文件 (多选):</label>
                    <select name="url_files" id="url_files" multiple required>
                        {% for file in available_files %}
                        <option value="{{ file }}">{{ file }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="default_file">默认文件:</label>
                <select name="default_file" id="default_file" required>
                    <option value="">请选择默认文件</option>
                    {% for file in available_files %}
                    <option value="{{ file }}">{{ file }}</option>
                    {% endfor %}
                </select>
            </div>
            <button type="submit" class="btn">创建URL</button>
        </form>

        <div class="config-list">
            <h3>📋 已创建URL</h3>
            {% for url_path, config in url_configs.items() %}
            <div class="config-item">
                <h4>{{ url_path }}</h4>
                <p><strong>关联文件:</strong> {{ config.files|join(', ') }}</p>
                <p><strong>默认文件:</strong> {{ config.default_file }}</p>
                <form method="post" action="/admin/delete_url_config" style="display: inline;">
                    <input type="hidden" name="url_path" value="{{ url_path }}">
                    <button type="submit" class="btn btn-danger" onclick="return confirm('确定删除此URL配置吗？')">删除</button>
                </form>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- 全局设置标签页 -->
    <div id="settings" class="tab-content">
        <h2>⚙️ 全局设置</h2>
        <form method="post" action="/admin/save_global_settings">
            <div class="form-row">
                <div>
                    <label for="default_threshold">默认阈值:</label>
                    <input type="number" name="default_threshold" id="default_threshold" 
                           value="{{ global_settings.default_threshold }}" min="1" required>
                </div>
                <div>
                    <label for="default_highlight_num">默认高光个数:</label>
                    <input type="number" name="default_highlight_num" id="default_highlight_num" 
                           value="{{ global_settings.default_highlight_num }}" min="1" required>
                </div>
            </div>
            <div class="form-group">
                <label for="default_notification">默认通知内容:</label>
                <input type="text" name="default_notification" id="default_notification" 
                       value="{{ global_settings.default_notification }}" required>
            </div>
            <button type="submit" class="btn">保存设置</button>
        </form>

        <hr style="margin: 30px 0;">
        
        <h3>🔐 修改密码</h3>
        <form method="post" action="/admin/change_password">
            <div class="form-group">
                <label for="new_password">新密码:</label>
                <input type="password" name="new_password" id="new_password" required>
            </div>
            <div class="form-group">
                <label for="confirm_password">确认密码:</label>
                <input type="password" name="confirm_password" id="confirm_password" required>
            </div>
            <button type="submit" class="btn">修改密码</button>
        </form>
    </div>

    <script>
        // 标签页切换
        function showTab(tabName) {
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            event.target.classList.add('active');
            document.getElementById(tabName).classList.add('active');
        }

        // 文件配置数据
        const fileConfigs = {{ file_configs|tojson }};

        // 加载文件配置
        function loadFileConfig() {
            const fileName = document.getElementById('file_name').value;
            if (!fileName) return;

            const config = fileConfigs[fileName] || {
                threshold: {{ global_settings.default_threshold }},
                highlight_num: {{ global_settings.default_highlight_num }},
                notification: "{{ global_settings.default_notification }}",
                is_ph_file: false,
                is_ma: false,
                display_stages: [{name: '全部', start: 1, end: -1}]
            };

            document.getElementById('threshold').value = config.threshold;
            document.getElementById('highlight_num').value = config.highlight_num;
            document.getElementById('notification').value = config.notification;
            document.getElementById('is_ph_file').checked = config.is_ph_file;
            document.getElementById('is_ma').checked = config.is_ma;

            // 加载显示阶段
            const container = document.getElementById('stages-container');
            container.innerHTML = '';
            config.display_stages.forEach((stage, index) => {
                addStageWithData(stage.name, stage.start, stage.end);
            });
        }

        // 添加阶段
        function addStage() {
            addStageWithData('', '', '');
        }

        function addStageWithData(name, start, end) {
            const container = document.getElementById('stages-container');
            const div = document.createElement('div');
            div.className = 'stage-input';
            div.innerHTML = `
                <input type="text" name="stage_names[]" placeholder="阶段名称" value="${name}" required>
                <input type="number" name="stage_starts[]" placeholder="开始" min="1" value="${start}" required>
                <input type="number" name="stage_ends[]" placeholder="结束(-1表示到末尾)" value="${end}" required>
                <button type="button" onclick="removeStage(this)">删除</button>
            `;
            container.appendChild(div);
            
            // 如果是第一个阶段，隐藏删除按钮
            if (container.children.length === 1) {
                div.querySelector('button').style.display = 'none';
            }
        }

        // 删除阶段
        function removeStage(button) {
            const container = document.getElementById('stages-container');
            if (container.children.length > 1) {
                button.parentElement.remove();
                // 确保第一个阶段的删除按钮隐藏
                if (container.children.length > 0) {
                    container.children[0].querySelector('button').style.display = 'none';
                }
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (document.getElementById('stages-container').children.length === 0) {
                addStageWithData('全部', 1, -1);
            }
        });
    </script>
</body>
</html>
'''
