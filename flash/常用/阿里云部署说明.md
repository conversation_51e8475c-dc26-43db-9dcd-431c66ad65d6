# 阿里云服务器部署说明

## 🚀 快速部署（简化版本）

如果您遇到模块导入问题，可以使用简化版本 `new_simple.py`，它不依赖外部配置文件。

### 1. 上传文件到服务器

```bash
# 上传以下文件到服务器
- new_simple.py  # 简化版主程序
- *.txt          # 所有数据文件
```

### 2. 安装依赖

```bash
# 安装Python依赖
pip install flask numpy

# 或者使用pip3
pip3 install flask numpy
```

### 3. 运行应用

```bash
# 运行简化版本
python new_simple.py

# 或者使用python3
python3 new_simple.py
```

### 4. 访问应用

```
http://您的服务器IP:7799
```

## 🛠️ 完整版部署（带管理员功能）

如果您想使用完整的管理员功能，需要上传所有文件：

### 1. 上传所有文件

```bash
# 上传以下文件到服务器
- new.py                    # 完整版主程序
- config_manager.py         # 配置管理器
- admin_templates.py        # 管理员页面模板
- admin_config_example.json # 配置示例
- *.txt                     # 所有数据文件
```

### 2. 安装依赖

```bash
pip install flask numpy
```

### 3. 运行应用

```bash
python new.py
```

### 4. 访问管理员后台

```
# 管理员登录页面
http://您的服务器IP:7799/admin/login

# 默认密码：admin123
```

## 🔧 常见问题解决

### 问题1：ModuleNotFoundError

**错误信息：**
```
ModuleNotFoundError: No module named 'config_manager'
```

**解决方案：**
1. 使用简化版本 `new_simple.py`
2. 或者确保所有文件都在同一目录下

### 问题2：端口被占用

**错误信息：**
```
Address already in use
```

**解决方案：**
```bash
# 查找占用端口的进程
lsof -i :7799

# 杀死进程
kill -9 进程ID

# 或者修改端口
# 在代码最后一行修改端口号
app.run(host='0.0.0.0', port=8080, debug=True)
```

### 问题3：权限问题

**解决方案：**
```bash
# 给文件执行权限
chmod +x new_simple.py

# 或者使用sudo运行
sudo python new_simple.py
```

## 📁 文件结构

```
项目目录/
├── new_simple.py           # 简化版主程序（推荐）
├── new.py                  # 完整版主程序
├── config_manager.py       # 配置管理器
├── admin_templates.py      # 管理员模板
├── admin_config.json       # 配置文件（自动生成）
├── yy.txt                  # 数据文件
├── kun.txt                 # 数据文件
└── ...                     # 其他数据文件
```

## 🌟 功能对比

| 功能 | 简化版 (new_simple.py) | 完整版 (new.py) |
|------|------------------------|-----------------|
| 基本数据展示 | ✅ | ✅ |
| 动态配置阶段 | ✅ | ✅ |
| 自动文件识别 | ✅ | ✅ |
| 管理员后台 | ❌ | ✅ |
| 动态URL创建 | ❌ | ✅ |
| 密码保护 | ❌ | ✅ |
| 配置持久化 | ❌ | ✅ |

## 💡 推荐部署方案

### 方案1：快速部署（推荐新手）
- 使用 `new_simple.py`
- 无需额外配置
- 功能完整，满足基本需求

### 方案2：完整部署（推荐高级用户）
- 使用 `new.py`
- 需要上传所有文件
- 支持管理员后台和高级功能

## 🔒 安全建议

1. **修改默认端口**：避免使用默认的7799端口
2. **设置防火墙**：只开放必要的端口
3. **修改密码**：如果使用完整版，请立即修改默认密码
4. **使用HTTPS**：在生产环境中配置SSL证书

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本（建议3.7+）
2. 依赖包是否正确安装
3. 文件权限是否正确
4. 端口是否被占用
5. 数据文件是否存在

## 🎉 部署成功标志

当您看到以下信息时，表示部署成功：

```
* Running on all addresses (0.0.0.0)
* Running on http://127.0.0.1:7799
* Running on http://[您的服务器IP]:7799
```

然后就可以通过浏览器访问您的应用了！
