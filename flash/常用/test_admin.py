#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员功能测试脚本
"""

from config_manager import config_manager

def test_config_manager():
    """测试配置管理器功能"""
    print("🧪 测试配置管理器功能...")
    
    # 测试密码验证
    print("\n1. 测试密码验证:")
    print(f"默认密码验证: {config_manager.verify_password('admin123')}")
    
    # 测试文件配置
    print("\n2. 测试文件配置:")
    test_config = {
        'threshold': 200,
        'highlight_num': 25,
        'display_stages': [
            {'name': '1-10', 'start': 1, 'end': 10},
            {'name': '11-25', 'start': 11, 'end': 25}
        ],
        'notification': '🧪 测试配置',
        'is_ph_file': False,
        'is_ma': False
    }
    
    # 保存测试配置
    result = config_manager.set_file_config('test_file', test_config)
    print(f"保存测试配置: {result}")
    
    # 读取测试配置
    loaded_config = config_manager.get_file_config('test_file')
    print(f"读取测试配置: {loaded_config}")
    
    # 测试URL配置
    print("\n3. 测试URL配置:")
    url_config = {
        'files': ['test_file', 'another_file'],
        'default_file': 'test_file'
    }
    
    result = config_manager.set_url_config('/test', url_config)
    print(f"保存URL配置: {result}")
    
    loaded_url_config = config_manager.get_url_config('/test')
    print(f"读取URL配置: {loaded_url_config}")
    
    # 测试获取可用文件
    print("\n4. 测试获取可用文件:")
    available_files = config_manager.get_available_files()
    print(f"可用文件: {available_files}")
    
    # 测试全局设置
    print("\n5. 测试全局设置:")
    global_settings = config_manager.get_global_settings()
    print(f"全局设置: {global_settings}")
    
    print("\n✅ 配置管理器测试完成!")

def show_current_config():
    """显示当前配置"""
    print("\n📋 当前配置概览:")
    print("=" * 50)
    
    print("\n📁 文件配置:")
    file_configs = config_manager.get_all_file_configs()
    for file_name, config in file_configs.items():
        print(f"  {file_name}:")
        print(f"    阈值: {config.get('threshold', 'N/A')}")
        print(f"    高光个数: {config.get('highlight_num', 'N/A')}")
        print(f"    显示阶段: {len(config.get('display_stages', []))} 个")
        print(f"    通知: {config.get('notification', 'N/A')}")
        print()
    
    print("🔗 URL配置:")
    url_configs = config_manager.get_all_url_configs()
    for url_path, config in url_configs.items():
        print(f"  {url_path}:")
        print(f"    关联文件: {config.get('files', [])}")
        print(f"    默认文件: {config.get('default_file', 'N/A')}")
        print()
    
    print("⚙️ 全局设置:")
    global_settings = config_manager.get_global_settings()
    for key, value in global_settings.items():
        print(f"  {key}: {value}")

if __name__ == '__main__':
    print("🚀 管理员功能测试")
    print("=" * 50)
    
    # 运行测试
    test_config_manager()
    
    # 显示当前配置
    show_current_config()
    
    print("\n🎉 测试完成!")
    print("\n💡 提示:")
    print("1. 访问 /admin/login 登录管理员后台")
    print("2. 默认密码: admin123")
    print("3. 登录后可以配置文件参数和创建URL路由")
