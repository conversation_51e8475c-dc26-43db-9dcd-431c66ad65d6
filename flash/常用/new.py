from flask import Flask, render_template_string, request, session, redirect, url_for, jsonify
import os
import numpy as np
import logging
from datetime import datetime
from collections import deque
import json
import hashlib
import secrets
from config_manager import config_manager
from admin_templates import ADMIN_LOGIN_TEMPLATE, ADMIN_DASHBOARD_TEMPLATE

# 定义库存处理类
class InventoryProcessor:
    def __init__(self, threshold=None, highlight_num=40, is_ph_file=False, is_ma=False):
        # 统一阈值设置
        self.threshold = threshold if threshold is not None else (500 if is_ma else 270 if is_ph_file else 190)
        self.highlight_num = highlight_num
        self.is_ph_file = is_ph_file
        self.is_ma = is_ma

    @staticmethod
    def parse_time(time_str):
        time_str = time_str.strip().rstrip(':')
        for fmt in ["%Y-%m-%d %H:%M:%S", "%Y-%m-%d %H:%M"]:
            try:
                return datetime.strptime(time_str, fmt)
            except ValueError:
                continue
        logging.error(f"无法解析日期时间字符串: {time_str}")
        return datetime.min

    def process_queue(self, raw_value, pending, valid, time_window, current_ts):
        processed_value = int(round(raw_value))
        if processed_value < 0:
            qty = abs(processed_value)
            sale_record = {
                'timestamp': current_ts,
                'original': qty,
                'remaining': qty,
                'valid': qty <= self.threshold
            }
            pending.append(sale_record)
            if sale_record['valid']:
                valid.append(qty)
            return qty
        else:
            refund = abs(processed_value)
            for i in range(len(pending) - 1, -1, -1):
                record = pending[i]
                time_diff = (self.parse_time(current_ts) - self.parse_time(record['timestamp'])).total_seconds()
                if time_diff <= time_window and record['valid'] and record['original'] == refund:
                    try:
                        valid.remove(refund)
                    except ValueError:
                        pass
                    del pending[i]
                    return refund
            return 0

    def process_data(self, data):
        # 如果数据为空，直接返回空结果
        if not data:
            return [], [], 0, 0

        processed_data = []
        old_app_pending = deque(maxlen=100)
        shop_pending = deque(maxlen=100)
        new_app_pending = deque(maxlen=100)


        old_app_valid = []
        shop_valid = []
        new_app_valid = []

        total_sales = 0
        app_sales = 0

        # 预先分配足够的容量
        processed_data = [None] * len(data)
        idx = 0

        for line in data:
            if not line.strip():
                continue

            parts = line.split(',')
            if len(parts) < 5:
                continue

            try:
                ts = parts[0].strip()
                raw_sold = float(parts[2])  # 旧版app变化
                raw_stock = float(parts[4])  # 微店变化
                raw_new_app_change = 0.0
                if len(parts) >= 7:
                    raw_new_app_change = float(parts[6])  # 新版app变化

                # 筛选并计算总销量
                if abs(raw_sold) < self.threshold:
                    total_sales += -raw_sold
                if abs(raw_stock) < self.threshold:
                    total_sales += -raw_stock
                if abs(raw_new_app_change) < self.threshold:
                    total_sales += -raw_new_app_change


                # 计算 app 端的销量（旧版app + 新版app）
                if abs(raw_sold) <= self.threshold:
                    if raw_sold < 0:
                        app_sales += abs(raw_sold)
                    else:
                        app_sales -= raw_sold

                if abs(raw_new_app_change) <= self.threshold:
                    if raw_new_app_change < 0:
                        app_sales += abs(raw_new_app_change)
                    else:
                        app_sales -= raw_new_app_change

                self.process_queue(raw_sold, old_app_pending, old_app_valid, 1980, ts)
                self.process_queue(raw_stock, shop_pending, shop_valid, 1020, ts)
                if len(parts) >= 7:
                    self.process_queue(raw_new_app_change, new_app_pending, new_app_valid, 1020, ts)
                
                filtered = abs(raw_sold) > self.threshold or abs(raw_stock) > self.threshold or abs(raw_new_app_change) > self.threshold

                # 直接在指定位置存储，避免append的开销
                processed_data[idx] = (line, filtered, raw_sold, raw_stock, raw_new_app_change)
                idx += 1

            except Exception as e:
                logging.error(f"数据处理失败: {line} - {str(e)}")
                continue

        # 删除未使用的元素
        processed_data = processed_data[:idx]

        # 保持时间降序排列
        processed_data.reverse()

        # 高效合并并排序
        combined = sorted(old_app_valid + shop_valid + new_app_valid, reverse=True)
        if self.highlight_num < len(combined):
            combined = combined[:self.highlight_num]

        return processed_data, combined, total_sales, app_sales

    @staticmethod
    def calculate_stats(values):
        if not values:
            return {'min': 0, 'max': 0, 'median': 0, 'mean': 0}
        arr = np.array(values)
        return {
            'min': int(np.min(arr).item()),
            'max': int(np.max(arr).item()),
            'median': float(np.median(arr).item()),
            'mean': round(float(np.mean(arr)), 2)
        }

# 配置日志记录
logging.basicConfig(level=logging.INFO)
app = Flask(__name__)
app.secret_key = 'your_secure_secret_key_here'  # 请替换为安全的密钥
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 86400  # 静态资源缓存1天

@app.context_processor
def utility_processor():
    return dict(abs=abs)

# ==================== 管理员功能路由 ====================

def admin_required(f):
    """管理员权限装饰器"""
    def decorated_function(*args, **kwargs):
        if not session.get('admin_logged_in'):
            return redirect(url_for('admin_login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    """管理员登录页面"""
    if request.method == 'POST':
        password = request.form.get('password')
        if config_manager.verify_password(password):
            session['admin_logged_in'] = True
            return redirect(url_for('admin_dashboard'))
        else:
            return render_template_string(ADMIN_LOGIN_TEMPLATE, error='密码错误')

    return render_template_string(ADMIN_LOGIN_TEMPLATE)

@app.route('/admin/logout')
def admin_logout():
    """管理员退出登录"""
    session.pop('admin_logged_in', None)
    return redirect(url_for('admin_login'))

@app.route('/admin')
@admin_required
def admin_dashboard():
    """管理员控制台"""
    return render_template_string(ADMIN_DASHBOARD_TEMPLATE,
                                  available_files=config_manager.get_available_files(),
                                  file_configs=config_manager.get_all_file_configs(),
                                  url_configs=config_manager.get_all_url_configs(),
                                  global_settings=config_manager.get_global_settings())

@app.route('/admin/save_file_config', methods=['POST'])
@admin_required
def save_file_config():
    """保存文件配置"""
    try:
        file_name = request.form.get('file_name')

        # 处理显示阶段
        stage_names = request.form.getlist('stage_names[]')
        stage_starts = request.form.getlist('stage_starts[]')
        stage_ends = request.form.getlist('stage_ends[]')

        display_stages = []
        for i in range(len(stage_names)):
            if stage_names[i].strip():
                display_stages.append({
                    'name': stage_names[i].strip(),
                    'start': int(stage_starts[i]),
                    'end': int(stage_ends[i])
                })

        config = {
            'threshold': int(request.form.get('threshold')),
            'highlight_num': int(request.form.get('highlight_num')),
            'notification': request.form.get('notification'),
            'is_ph_file': 'is_ph_file' in request.form,
            'is_ma': 'is_ma' in request.form,
            'display_stages': display_stages
        }

        config_manager.set_file_config(file_name, config)

        return render_template_string(ADMIN_DASHBOARD_TEMPLATE,
                                      available_files=config_manager.get_available_files(),
                                      file_configs=config_manager.get_all_file_configs(),
                                      url_configs=config_manager.get_all_url_configs(),
                                      global_settings=config_manager.get_global_settings(),
                                      message=f'文件 {file_name} 配置保存成功')
    except Exception as e:
        return render_template_string(ADMIN_DASHBOARD_TEMPLATE,
                                      available_files=config_manager.get_available_files(),
                                      file_configs=config_manager.get_all_file_configs(),
                                      url_configs=config_manager.get_all_url_configs(),
                                      global_settings=config_manager.get_global_settings(),
                                      error=f'保存配置失败: {str(e)}')

@app.route('/admin/save_url_config', methods=['POST'])
@admin_required
def save_url_config():
    """保存URL配置"""
    try:
        url_path = request.form.get('url_path').strip()
        if not url_path.startswith('/'):
            url_path = '/' + url_path

        files = request.form.getlist('url_files')
        default_file = request.form.get('default_file')

        config = {
            'files': files,
            'default_file': default_file
        }

        config_manager.set_url_config(url_path, config)

        # 动态创建路由
        create_dynamic_route(url_path, files, default_file)

        return render_template_string(ADMIN_DASHBOARD_TEMPLATE,
                                      available_files=config_manager.get_available_files(),
                                      file_configs=config_manager.get_all_file_configs(),
                                      url_configs=config_manager.get_all_url_configs(),
                                      global_settings=config_manager.get_global_settings(),
                                      message=f'URL {url_path} 创建成功')
    except Exception as e:
        return render_template_string(ADMIN_DASHBOARD_TEMPLATE,
                                      available_files=config_manager.get_available_files(),
                                      file_configs=config_manager.get_all_file_configs(),
                                      url_configs=config_manager.get_all_url_configs(),
                                      global_settings=config_manager.get_global_settings(),
                                      error=f'创建URL失败: {str(e)}')

@app.route('/admin/delete_url_config', methods=['POST'])
@admin_required
def delete_url_config():
    """删除URL配置"""
    try:
        url_path = request.form.get('url_path')
        config_manager.delete_url_config(url_path)

        return render_template_string(ADMIN_DASHBOARD_TEMPLATE,
                                      available_files=config_manager.get_available_files(),
                                      file_configs=config_manager.get_all_file_configs(),
                                      url_configs=config_manager.get_all_url_configs(),
                                      global_settings=config_manager.get_global_settings(),
                                      message=f'URL {url_path} 删除成功')
    except Exception as e:
        return render_template_string(ADMIN_DASHBOARD_TEMPLATE,
                                      available_files=config_manager.get_available_files(),
                                      file_configs=config_manager.get_all_file_configs(),
                                      url_configs=config_manager.get_all_url_configs(),
                                      global_settings=config_manager.get_global_settings(),
                                      error=f'删除URL失败: {str(e)}')

@app.route('/admin/save_global_settings', methods=['POST'])
@admin_required
def save_global_settings():
    """保存全局设置"""
    try:
        settings = {
            'default_threshold': int(request.form.get('default_threshold')),
            'default_highlight_num': int(request.form.get('default_highlight_num')),
            'default_notification': request.form.get('default_notification')
        }

        config_manager.set_global_settings(settings)

        return render_template_string(ADMIN_DASHBOARD_TEMPLATE,
                                      available_files=config_manager.get_available_files(),
                                      file_configs=config_manager.get_all_file_configs(),
                                      url_configs=config_manager.get_all_url_configs(),
                                      global_settings=config_manager.get_global_settings(),
                                      message='全局设置保存成功')
    except Exception as e:
        return render_template_string(ADMIN_DASHBOARD_TEMPLATE,
                                      available_files=config_manager.get_available_files(),
                                      file_configs=config_manager.get_all_file_configs(),
                                      url_configs=config_manager.get_all_url_configs(),
                                      global_settings=config_manager.get_global_settings(),
                                      error=f'保存全局设置失败: {str(e)}')

@app.route('/admin/change_password', methods=['POST'])
@admin_required
def change_password():
    """修改管理员密码"""
    try:
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        if new_password != confirm_password:
            raise ValueError('两次输入的密码不一致')

        if len(new_password) < 6:
            raise ValueError('密码长度至少6位')

        config_manager.change_password(new_password)

        return render_template_string(ADMIN_DASHBOARD_TEMPLATE,
                                      available_files=config_manager.get_available_files(),
                                      file_configs=config_manager.get_all_file_configs(),
                                      url_configs=config_manager.get_all_url_configs(),
                                      global_settings=config_manager.get_global_settings(),
                                      message='密码修改成功')
    except Exception as e:
        return render_template_string(ADMIN_DASHBOARD_TEMPLATE,
                                      available_files=config_manager.get_available_files(),
                                      file_configs=config_manager.get_all_file_configs(),
                                      url_configs=config_manager.get_all_url_configs(),
                                      global_settings=config_manager.get_global_settings(),
                                      error=f'修改密码失败: {str(e)}')

def create_dynamic_route(url_path, files, default_file):
    """动态创建路由"""
    def dynamic_route():
        if request.method == 'GET':
            session['current_file'] = f'{default_file}.txt'
        return process_request(files, f'{default_file}.txt')

    dynamic_route.__name__ = f'dynamic_route_{url_path.replace("/", "_")}'
    app.add_url_rule(url_path, dynamic_route.__name__, dynamic_route, methods=['GET', 'POST'])

# ==================== 管理员功能结束 ====================


def get_available_files():
    file_paths = {}
    current_dir = os.getcwd()  # 获取当前工作目录

    # 遍历当前目录下的所有文件
    for file in os.listdir(current_dir):
        if file.endswith('.txt'):
            # 使用文件名(不带扩展名)作为键
            key = os.path.splitext(file)[0]
            file_paths[key] = file

    return file_paths

# 获取可用的文件列表
def get_available_buttons():
    return list(get_available_files().keys())

template = '''
<html>
<head>
    <title>圆梦大使数据网站，请勿外传</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <!-- 引入Excel导出库 -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <style>
        :root {
            --primary-color: #4CAF50;
            --secondary-color: #f8f9fa;
            --text-color: #212529;
        }
        
        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 8px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .container {
            max-width: 100%;
            overflow-x: auto;
        }

        .data-header {
            text-align: center;
            margin: 12px 0;
            font-size: 1.5rem;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .button-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
            gap: 5px;
            margin: 12px 0;
        }

        button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: opacity 0.2s;
        }

        button:active {
            opacity: 0.8;
        }

        /* 表格容器样式 */
        .table-container {
            max-height: 500px;
            overflow-y: auto;
            border: 2px solid #e3f2fd;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            margin: 15px 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.78rem;
            table-layout: fixed;
            background: white;
        }

        th, td {
            padding: 12px 8px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
            word-break: break-all;
            transition: background-color 0.3s ease;
        }

        th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
            font-size: 0.85rem;
            position: sticky;
            top: 0;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        tbody tr:hover {
            background-color: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 美化的颜色定义 - 更浅的颜色 */
        .positive-change {
            background: linear-gradient(135deg, #fef7f7 0%, #fdf2f8 100%);
            border-left: 4px solid #fca5a5;
            color: #dc2626;
        }
        .positive-change:hover {
            background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
        }

        .unfiltered {
            background: linear-gradient(135deg, #f7fdf7 0%, #f0fdf4 100%);
            border-left: 4px solid #86efac;
            color: #16a34a;
        }
        .unfiltered:hover {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        }

        .filtered {
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
            border-left: 4px solid #fbbf24;
            color: #d97706;
        }
        .filtered:hover {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        }

        .stats-box {
            background: var(--secondary-color);
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
            font-size: 0.85rem;
        }

        .highlight-section {
            margin: 12px 0;
            padding: 8px;
            background: #f3f4f6;
            border-radius: 6px;
        }

        .highlight-group {
            margin-top: 6px;
        }

        .highlight-block {
            margin: 6px 0;
        }

        .highlight-block h4 {
            margin: 4px 0;
            color: #666;
            font-size: 0.78rem;
        }

        .highlight-row {
            display: flex;
            flex-wrap: wrap;
            gap: 2px;
            margin: 3px 0;
        }

        .highlight-item {
            flex: 0 0 calc(6.66% - 2px);
            text-align: center;
            padding: 2px;
            background: #e3f2fd;
            border-radius: 2px;
            font-weight: 500;
            font-size: 0.68rem;
            min-width: 20px;
            line-height: 1.2;
        }

        input[type="number"] {
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 90px;
            margin: 0 3px;
            font-size: 0.8rem;
        }

        .form-group {
            margin: 6px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            align-items: center;
        }
        
        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: black;
        }
        
        .help-button {
            background: #2196F3;
            margin-left: 5px;
        }
        
        /* 有颜色的文字加深颜色 */
        .text-green {
            color: #006400;
            font-weight: 500;
        }
        
        .text-red {
            color: #8B0000;
            font-weight: 500;
        }
        
        .text-yellow {
            color: #8B8000;
            font-weight: 500;
        }
        
        /* 状态标签样式 */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-normal {
            background: linear-gradient(135deg, #86efac 0%, #6ee7b7 100%);
            color: #065f46;
            box-shadow: 0 2px 4px rgba(134, 239, 172, 0.3);
        }

        .status-refund {
            background: linear-gradient(135deg, #fca5a5 0%, #f87171 100%);
            color: #7f1d1d;
            box-shadow: 0 2px 4px rgba(252, 165, 165, 0.3);
        }

        .status-water {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: #78350f;
            box-shadow: 0 2px 4px rgba(251, 191, 36, 0.3);
        }

        /* 自动刷新选项样式 */
        .auto-refresh {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.85rem;
            margin-left: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .auto-refresh-inline {
            display: flex;
            align-items: center;
            padding: 0 5px;
            border-radius: 4px;
            font-size: 0.75rem;
            margin-left: 5px;
            background: #e8f5e9;
            height: 100%;
            white-space: nowrap;
        }
        
        .export-btn {
            background-color: #3f51b5;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 10px;
            font-size: 0.75rem;
            cursor: pointer;
            margin-left: 5px;
            white-space: nowrap;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            transition: background-color 0.3s;
        }
        
        .export-btn:hover {
            background-color: #303f9f;
        }
        
        .auto-refresh input, .auto-refresh-inline input {
            margin-right: 5px;
        }
        
        .tooltip {
            position: relative;
            display: inline-block;
            margin-left: 5px;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #555;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.75rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        @media (max-width: 480px) {
            table {
                font-size: 0.7rem;
            }

            th, td {
                padding: 4px;
                font-size: 0.65rem;
                white-space: normal;
            }
            
            button {
                padding: 6px;
                font-size: 0.72rem;
            }
            
            .data-header {
                font-size: 1.3rem;
                flex-wrap: wrap;
            }

            .highlight-item {
                flex: 0 0 calc(10% - 2px);
                font-size: 0.6rem;
                min-width: 18px;
            }
            
            .highlight-block h4 {
                font-size: 0.7rem;
            }

            td:first-child {
                min-width: 50px;
                max-width: 60px;
            }
            
            .modal-content {
                margin: 30% auto;
                width: 90%;
                padding: 15px;
            }
            
            .auto-refresh-inline {
                font-size: 0.65rem;
                padding: 0 3px;
                margin-left: 3px;
            }
            
            .export-btn {
                font-size: 0.65rem;
                padding: 4px 6px;
                margin-left: 3px;
            }
            
            /* 优化移动端数据面板 */
            .data-dashboard {
                padding: 6px;
                margin: 6px 0;
            }
            
            .dashboard-stats {
                grid-template-columns: repeat(3, 1fr);
                gap: 2px;
                font-size: 0.6rem;
                margin-bottom: 5px;
            }
            
            .stat-item {
                padding: 1px 2px;
            }
            
            .highlight-stats {
                gap: 2px;
                font-size: 0.6rem;
            }
            
            .dashboard-header h3 {
                font-size: 0.8rem;
                margin-bottom: 3px;
            }
        }

        @media (max-width: 360px) {
            th, td {
                padding: 3px;
                font-size: 0.6rem;
            }

            .highlight-item {
                flex: 0 0 calc(12% - 2px);
                font-size: 0.56rem;
            }

            input[type="number"] {
                width: 80px;
                padding: 5px;
            }
        }

        .stats-box {
            background: var(--secondary-color);
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
            font-size: 0.85rem;
        }

        .highlight-section {
            margin: 12px 0;
            padding: 8px;
            background: #f3f4f6;
            border-radius: 6px;
        }

        .highlight-group {
            margin-top: 6px;
        }

        .highlight-block {
            margin: 6px 0;
        }

        .highlight-block h4 {
            margin: 4px 0;
            color: #666;
            font-size: 0.78rem;
        }

        .highlight-row {
            display: flex;
            flex-wrap: wrap;
            gap: 2px;
            margin: 3px 0;
        }

        .highlight-item {
            flex: 0 0 calc(6.66% - 2px);
            text-align: center;
            padding: 2px;
            background: #e3f2fd;
            border-radius: 2px;
            font-weight: 500;
            font-size: 0.68rem;
            min-width: 20px;
            line-height: 1.2;
        }
        
        /* 组合数据区域 */
        .data-dashboard {
            background: var(--secondary-color);
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
            flex-wrap: wrap;
        }
        
        .dashboard-header h3 {
            margin: 0;
            font-size: 0.95rem;
            color: var(--primary-color);
        }
        
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            margin-bottom: 8px;
            font-size: 0.85rem;
        }
        
        .stat-item {
            padding: 2px 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: center;
        }
        
        .dashboard-highlights {
            margin-top: 5px;
        }
        
        .highlight-header {
            font-size: 0.9rem;
            margin: 5px 0;
            color: #666;
        }
        
        .highlight-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            margin: 5px 0;
            font-size: 0.78rem;
        }

        /* 美化滚动条 */
        .table-container::-webkit-scrollbar {
            width: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #86efac 0%, #6ee7b7 100%);
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #6ee7b7 0%, #34d399 100%);
        }

        /* 添加表格动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .table-container {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="data-header">
        <span>{{ notification }} - {{ file_name|upper }}</span>
    </div>
    
    <!-- 隐藏字段存储当前文件名 -->
    <input type="hidden" id="currentFileName" value="{{ file_name }}">
    
    {% if show_buttons %}
    <form method="post">
        <div class="button-group">
            {% for name in buttons %}
            <button type="submit" name="{{ name }}">{{ name|upper }}</button>
            {% endfor %}
            <button type="button" class="help-button" id="helpButton">使用说明</button>
        </div>
    </form>
    {% endif %}

    <!-- 使用说明弹窗 -->
    <div id="helpModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>📌 使用说明</h3>
            <p>⏱️ 网站实时更新，点击绿色成员按键会刷新一次，数据标<span class="text-green">绿色</span>则为正常数据，数据标<span class="text-red">红色</span>则为库存退回数据，数据标<span class="text-yellow">黄色</span>则为注水数据，因为kms会有智能注水，所以会多拉一些高光数来判断；</p>
            <p>⚙️ 注水范围以及显示个数可以通过填空来设置，如果不会就不需要填空，默认我的选择；</p>
            <p>⚠️ 求稳建议下到阶段的中位数以上；</p>
            <p>🔍 影通一般，总销量÷（抽选人数+10）附近波动，线下签售按照抽选人数进行判断，经供参考；</p>
            <p>🔄 勾选<strong>自动刷新</strong>选项可以让页面每秒自动刷新一次，获取最新数据！</p>
        </div>
    </div>

    <div class="container">
        <form method="post">
            <div class="form-group">
                <input type="number" name="threshold" placeholder="注水数据范围" step="1" min="0">
                <input type="number" name="highlight_num" placeholder="显示数量个数" min="1">
                <button type="submit">设置</button>
            </div>
            <div class="form-group">
                <input type="number" name="cut_value" placeholder="总销÷" step="0.1" min="0">
                <button type="submit">计算</button>
                <div class="auto-refresh-inline">
                    <input type="checkbox" id="autoRefresh"> 
                    <label for="autoRefresh">自动刷新</label>
                    <div class="tooltip">ℹ️
                        <span class="tooltiptext">勾选后每3秒自动刷新页面，取消勾选停止刷新</span>
                    </div>
                </div>
                <button type="button" id="exportExcel" class="export-btn"></button>
            </div>
        </form>

        <div class="data-dashboard">
            <div class="dashboard-header">
                <h3>📊 数据统计 & 🌟 高光数据（去卡单） (TOP {{ highlight_num }})</h3>
            </div>
            
            <div class="dashboard-stats">
                {% if total is not none %}
                <div class="stat-item">🔢 总销: {{ total }}</div>
                <div class="stat-item">📱 APP销量: {{ kms_total }}</div>
                {% if stats is not none %}
                <div class="stat-item">⬇️ 最小值: {{ stats.min }}</div>
                {% endif %}
                {% endif %}
                
                {% if stats is not none %}
                <div class="stat-item">⬆️ 最大值: {{ stats.max }}</div>
                <div class="stat-item">📏 中位数: {{ stats.median }}</div>
                <div class="stat-item">📊 平均数: {{ stats.mean }}</div>
                {% endif %}
                
                {% if cut is not none %}
                <div class="stat-item">🧮 计算值: {{ cut }}</div>
                {% endif %}
                
                {% if file_name == 'ive' %}
                <div class="dashboard-stats">
                    <div class="stat-item">1-50最小: {{ stats_1_50.min }}</div>
                    <div class="stat-item">最大: {{ stats_1_50.max }}</div>
                    <div class="stat-item">中位: {{ stats_1_50.median }}</div>
                    
                    <div class="stat-item">51-200最小: {{ stats_51_200.min }}</div>
                    <div class="stat-item">最大: {{ stats_51_200.max }}</div>
                    <div class="stat-item">中位: {{ stats_51_200.median }}</div>
                </div>
                {% endif %}
                
                {% if file_name == '' %}
                <div class="dashboard-stats">
                    <div class="stat-item">1-50最小: {{ stats_1_50.min }}</div>
                    <div class="stat-item">最大: {{ stats_1_50.max }}</div>
                    <div class="stat-item">中位: {{ stats_1_50.median }}</div>
                    
                    <div class="stat-item">51-200最小: {{ stats_51_250.min }}</div>
                    <div class="stat-item">最大: {{ stats_51_250.max }}</div>
                    <div class="stat-item">中位: {{ stats_51_250.median }}</div>
                </div>
                {% endif %}
            </div>
            
            <div class="dashboard-highlights">
                <div class="highlight-group">
                <!-- 动态显示阶段 -->
                {% for stage_name, stage_data in stage_stats.items() %}
                    <div class="highlight-block">
                        <h4>{{ stage_name }}:</h4>
                        {% if stage_data.stats %}
                        <h4>最小 {{ stage_data.stats.min }} | 最大 {{ stage_data.stats.max }} | 中位 {{ stage_data.stats.median }} | 平均 {{ stage_data.stats.mean }}</h4>
                        {% endif %}
                        <div class="highlight-row">
                            {% for value in stage_data.values %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
                {% elif file_name in ['yenasz','yenagz'] %}
                    {% if stats_1_5 is not none %}
                    <div class="highlight-block">
                        <h4>1 - 30:</h4>
                        <h4>最小 {{ stats_1_5.min }} | 最大 {{ stats_1_5.max }} | 中位 {{ stats_1_5.median }} | 平均 {{ stats_1_5.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:30] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% if stats_6_10 is not none %}
                    <div class="highlight-block">
                        <h4>31 - 100:</h4>
                        <h4>最小 {{ stats_6_10.min }} | 最大 {{ stats_6_10.max }} | 中位 {{ stats_6_10.median }} | 平均 {{ stats_6_10.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[30:100] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name == '签名应募' %}
                    {% if stats_1_60 is not none %}
                    <div class="highlight-block">
                        <h4>1 - 50:</h4>
                        <h4>最小 {{ stats_1_60.min }} | 最大 {{ stats_1_60.max }} | 中位 {{ stats_1_60.median }} | 平均 {{ stats_1_60.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:50] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% if stats_61_260 is not none %}
                    <div class="highlight-block">
                        <h4>51 - 200:</h4>
                        <h4>最小 {{ stats_61_260.min }} | 最大 {{ stats_61_260.max }} | 中位 {{ stats_61_260.median }} | 平均 {{ stats_61_260.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[50:200] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name in ['合影应募','smn','allen', 'hyeongjun', 'jungmo', 'minhee', 'seongmin', 'serim', 'taeyoung', 'wonjin', 'woobin'] %}
                    {% if stats_1_10 is not none %}
                    <div class="highlight-block">
                        <h4>1 - 5:</h4>
                        <h4>最小 {{ stats_1_10.min }} | 最大 {{ stats_1_10.max }} | 中位 {{ stats_1_10.median }} | 平均 {{ stats_1_10.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:5] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% if stats_15_20 is not none %}
                    <div class="highlight-block">
                        <h4>6 - 10:</h4>
                        <h4>最小 {{ stats_15_20.min }} | 最大 {{ stats_15_20.max }} | 中位 {{ stats_15_20.median }} | 平均 {{ stats_15_20.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[5:10] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name in ['single', '2'] %}
                    {% if stats_1_50 is not none %}
                    <div class="highlight-block">
                        <h4>1 - 40:</h4>
                        <h4>最小 {{ stats_1_50.min }} | 最大 {{ stats_1_50.max }} | 中位 {{ stats_1_50.median }} | 平均 {{ stats_1_50.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:40] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    {% if stats_51_190 is not none %}
                    <div class="highlight-block">
                        <h4>41 - 80:</h4>
                        <h4>最小 {{ stats_51_190.min }} | 最大 {{ stats_51_190.max }} | 中位 {{ stats_51_190.median }} | 平均 {{ stats_51_190.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[40:80] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name in ['kickflip', 'ahof'] %}
                    {% if stats_1_50 is not none %}
                    <div class="highlight-block">
                        <h4>1 - 50:</h4>
                        <h4>最小 {{ stats_1_50.min }} | 最大 {{ stats_1_50.max }} | 中位 {{ stats_1_50.median }} | 平均 {{ stats_1_50.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:50] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    <div class="highlight-block">
                        <h4>51 - 150:</h4>
                          <h4>最小 {{ stats_51_125.min }} | 最大 {{ stats_51_125.max }} | 中位 {{ stats_51_125.median }} | 平均 {{ stats_51_125.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[50:150] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                {% elif file_name in [ 'shuhua','miyeon','yuqi','minnie','soyeon'] %}
                    <div class="highlight-block">
                        <h4>1 - 5:</h4>
                        <h4>最小 {{ stats_1_25.min }} | 最大 {{ stats_1_25.max }} | 中位 {{ stats_1_25.median }} | 平均 {{ stats_1_25.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:5] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="highlight-block">
                        <h4>6 - 10:</h4>
                        <h4>最小 {{ stats_26_50.min }} | 最大 {{ stats_26_50.max }} | 中位 {{ stats_26_50.median }} | 平均 {{ stats_26_50.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[6:10] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                {% elif file_name in [] %}
                    <div class="highlight-block">
                        <h4>1 -10:</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:10]if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                {% elif file_name == 'all' %}
                    {% if stats_176_275 is not none %}
                    <div class="highlight-block">
                        <h4>176 - 275:</h4>
                        <h4>最小 {{ stats_176_275.min }} | 最大 {{ stats_176_275.max }} | 中位 {{ stats_176_275.median }} | 平均 {{ stats_176_275.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[175:275] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% elif file_name in ['sakuya', 'riku', 'yushi', 'jaehee', 'ryo', 'sion'] %}
                    <div class="highlight-block">
                        <h4>1 - 25:</h4>
                        <h4>最小 {{ stats_1_25.min }} | 最大 {{ stats_1_25.max }} | 中位 {{ stats_1_25.median }} | 平均 {{ stats_1_25.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:25] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="highlight-block">
                        <h4>26 - 40:</h4>
                        <h4>最小 {{ stats_26_40.min }} | 最大 {{ stats_26_40.max }} | 中位 {{ stats_26_40.median }} | 平均 {{ stats_26_40.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[25:40] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    </div>
                {% elif file_name in ['anton', 'eunseok', 'shotaro', 'sohee', 'sungchan', 'wonbin','mark'] %}
                    <div class="highlight-block">
                        <h4>1 - 20:</h4>
                        <h4>最小 {{ stats_1_15.min }} | 最大 {{ stats_1_15.max }} | 中位 {{ stats_1_15.median }} | 平均 {{ stats_1_15.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:20] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="highlight-block">
                        <h4>21 - 40:</h4>
                        <h4>最小 {{ stats_16_30.min }} | 最大 {{ stats_16_30.max }} | 中位 {{ stats_16_30.median }} | 平均 {{ stats_16_30.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[20:40] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                {% elif 'ph' in file_name %}
                    <div class="highlight-block">
                        <h4>1 - 5:</h4>
                        <h4>最小 {{ stats_1_6.min }} | 最大 {{ stats_1_6.max }} | 中位 {{ stats_1_6.median }} | 平均 {{ stats_1_6.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[:5] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="highlight-block">
                        <h4>6 - 10:</h4>
                        <h4>最小 {{ stats_7_12.min }} | 最大 {{ stats_7_12.max }} | 中位 {{ stats_7_12.median }} | 平均 {{ stats_7_12.mean }}</h4>
                        <div class="highlight-row">
                            {% for value in highlighted_values[5:10] if highlighted_values is not none %}
                            <span class="highlight-item">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
        </div>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th style="width: 25%">🕐 变动时间</th>
                        <th style="width: 25%">📱 变动渠道</th>
                        <th style="width: 25%">📊 变动数量</th>
                        <th style="width: 25%">🏷️ 变动状态</th>
                    </tr>
                </thead>
                <tbody>
                    {% for line, filtered, real_sold, real_stock, real_new_app_change in data %}
                    {% set parts = line.split(',') %}
                    {% if real_sold != 0 %}
                    <tr class="{% if real_sold > 0 %}positive-change{% elif not filtered %}unfiltered{% else %}filtered{% endif %}">
                        <td>{{ parts[0] }}</td>
                        <td>📱 旧版app</td>
                        <td>{{ parts[2] }}</td>
                        <td>
                            {% if real_sold > 0 %}
                                <span class="status-badge status-refund">🔄 退回</span>
                            {% elif not filtered %}
                                <span class="status-badge status-normal">✅ 正常</span>
                            {% else %}
                                <span class="status-badge status-water">💧 注水</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endif %}
                    {% if real_stock != 0 %}
                    <tr class="{% if real_stock > 0 %}positive-change{% elif not filtered %}unfiltered{% else %}filtered{% endif %}">
                        <td>{{ parts[0] }}</td>
                        <td>🏪 微店</td>
                        <td>{{ parts[4] }}</td>
                        <td>
                            {% if real_stock > 0 %}
                                <span class="status-badge status-refund">🔄 退回</span>
                            {% elif not filtered %}
                                <span class="status-badge status-normal">✅ 正常</span>
                            {% else %}
                                <span class="status-badge status-water">💧 注水</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endif %}
                    {% if parts|length >= 7 and real_new_app_change != 0 %}
                    <tr class="{% if real_new_app_change > 0 %}positive-change{% elif not filtered %}unfiltered{% else %}filtered{% endif %}">
                        <td>{{ parts[0] }}</td>
                        <td>📲 新版app</td>
                        <td>{{ parts[6] }}</td>
                        <td>
                            {% if real_new_app_change > 0 %}
                                <span class="status-badge status-refund">🔄 退回</span>
                            {% elif not filtered %}
                                <span class="status-badge status-normal">✅ 正常</span>
                            {% else %}
                                <span class="status-badge status-water">💧 注水</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endif %}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 弹窗脚本
        document.addEventListener('DOMContentLoaded', function() {
            var modal = document.getElementById('helpModal');
            var btn = document.getElementById('helpButton');
            var span = document.getElementsByClassName('close')[0];
            
            btn.onclick = function() {
                modal.style.display = "block";
            }
            
            span.onclick = function() {
                modal.style.display = "none";
            }
            
            window.onclick = function(event) {
                if (event.target == modal) {
                    modal.style.display = "none";
                }
            }
            
            // 自动刷新功能
            var autoRefreshCheckbox = document.getElementById('autoRefresh');
            var refreshIntervalId = null;
            
            // 确保autoRefresh元素存在
            if (autoRefreshCheckbox) {
                // 获取当前路由的URL路径
                var currentRoute = window.location.pathname;
                var storageKey = 'autoRefreshEnabled_' + currentRoute;
                
                // 从本地存储中恢复当前路由的自动刷新状态
                var savedState = localStorage.getItem(storageKey);
                if (savedState === 'true') {
                    autoRefreshCheckbox.checked = true;
                    startAutoRefresh();
                }
                
                // 监听复选框状态变化
                autoRefreshCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        startAutoRefresh();
                        localStorage.setItem(storageKey, 'true');
                    } else {
                        stopAutoRefresh();
                        localStorage.setItem(storageKey, 'false');
                    }
                });
                
                // 确保页面卸载时清除定时器
                window.addEventListener('beforeunload', function() {
                    if (refreshIntervalId !== null) {
                        clearInterval(refreshIntervalId);
                    }
                    // 如果复选框是选中状态，保存到localStorage
                    localStorage.setItem(storageKey, autoRefreshCheckbox.checked ? 'true' : 'false');
                });
            }
            
            // 开始自动刷新
            function startAutoRefresh() {
                // 确保没有重复的定时器
                if (refreshIntervalId !== null) {
                    clearInterval(refreshIntervalId);
                }
                
                // 获取当前文件名，确保刷新的是当前阅览文件
                var currentFileName = document.getElementById('currentFileName').value;
                
                refreshIntervalId = setInterval(function() {
                    // 查找当前文件名对应的按钮并点击
                    var buttons = document.querySelectorAll('form button[type="submit"]');
                    var currentButton = null;
                    
                    // 寻找匹配当前文件名的按钮
                    for (var i = 0; i < buttons.length; i++) {
                        if (buttons[i].name === currentFileName) {
                            currentButton = buttons[i];
                            break;
                        }
                    }
                    
                    if (currentButton) {
                        // 模拟点击按钮
                        currentButton.click();
                    } else {
                        // 如果找不到对应按钮，回退到传统刷新方式
                        window.location.reload();
                    }
                }, 3000);
            }
            
            // 停止自动刷新
            function stopAutoRefresh() {
                if (refreshIntervalId !== null) {
                    clearInterval(refreshIntervalId);
                    refreshIntervalId = null;
                }
            }
            
            // 导出Excel功能
            var exportBtn = document.getElementById('exportExcel');
            if (exportBtn) {
                // 设置导出按钮文字
                exportBtn.textContent = "导出Excel";
                exportBtn.addEventListener('click', function() {
                    exportToExcel();
                });
            }
            
            function exportToExcel() {
                try {
                    // 创建一个新的工作簿
                    const wb = XLSX.utils.book_new();
                    
                    // 获取当前文件名
                    const currentFileName = document.getElementById('currentFileName').value || '圆梦大使';
                    
                    // 准备数据
                    // 计算表格数据部分
                    const tableHeaders = Array.from(document.querySelectorAll('table thead th'))
                        .map(th => th.textContent.trim());
                    const tableColCount = tableHeaders.length;
                    
                    const tableRows = [];
                    document.querySelectorAll('table tbody tr').forEach(tr => {
                        const row = Array.from(tr.querySelectorAll('td'))
                            .map(td => td.textContent.trim());
                        tableRows.push(row);
                    });
                    
                    // 统计数据部分
                    let statsData = [];
                    
                    // 添加基本统计数据
                    statsData.push(['📊 数据统计']);
                    statsData.push([]);
                    
                    // 所有统计项目
                    const statItems = document.querySelectorAll('.stat-item');
                    const statsRows = [];
                    statItems.forEach(item => {
                        if (item.textContent.trim()) {
                            statsRows.push([item.textContent.trim()]);
                        }
                    });
                    
                    // 按照每行3项重排统计数据
                    for (let i = 0; i < statsRows.length; i += 3) {
                        const row = [];
                        for (let j = 0; j < 3; j++) {
                            if (i + j < statsRows.length) {
                                row.push(statsRows[i + j][0]);
                            } else {
                                row.push('');
                            }
                        }
                        statsData.push(row);
                    }
                    
                    statsData.push([]);
                    statsData.push(['🌟 高光数据']);
                    statsData.push([]);
                    
                    // 处理高光数据
                    const highlightBlocks = document.querySelectorAll('.highlight-block');
                    
                    // 找到第一个高光块的标题和统计信息
                    if (highlightBlocks.length > 0) {
                        highlightBlocks.forEach(block => {
                            try {
                                const title = block.querySelector('h4:first-child');
                                if (title) {
                                    statsData.push([title.textContent.trim()]);
                                    
                                    const stats = block.querySelector('h4:nth-child(2)');
                                    if (stats) {
                                        statsData.push([stats.textContent.trim()]);
                                    }
                                    
                                    // 获取高光值并按5个一行显示
                                    const values = Array.from(block.querySelectorAll('.highlight-item'))
                                        .map(item => item.textContent.trim());
                                    
                                    for (let i = 0; i < values.length; i += 5) {
                                        const rowValues = values.slice(i, i + 5);
                                        while (rowValues.length < 5) rowValues.push('');
                                        statsData.push(rowValues);
                                    }
                                    
                                    statsData.push([]);
                                }
                            } catch (e) {
                                console.error('处理高光块时出错:', e);
                            }
                        });
                    }
                    
                    // 找出统计数据的最大列数
                    const statsColCount = statsData.reduce((max, row) => Math.max(max, row.length), 0);
                    
                    // 创建完整数据表
                    const aoa = [];
                    
                    // 标题行 - 跨越所有列
                    const totalColumns = tableColCount + 2 + statsColCount;
                    const titleRow = [currentFileName.toUpperCase() + ' 销售数据'];
                    for (let i = 1; i < totalColumns; i++) titleRow.push('');
                    aoa.push(titleRow);
                    
                    // 空行
                    aoa.push(Array(totalColumns).fill(''));
                    
                    // 表头行 - 分为左侧销售明细和右侧统计数据
                    const headerRow = [...tableHeaders];
                    // 添加两个空列作为分隔
                    headerRow.push('');
                    headerRow.push('');
                    // 添加统计数据标题
                    if (statsData[0] && statsData[0][0]) {
                        headerRow.push(statsData[0][0]);
                    } else {
                        headerRow.push('统计数据');
                    }
                    // 填充剩余列
                    for (let i = headerRow.length; i < totalColumns; i++) headerRow.push('');
                    aoa.push(headerRow);
                    
                    // 数据行
                    const maxRows = Math.max(tableRows.length, statsData.length - 1);
                    
                    for (let i = 0; i < maxRows; i++) {
                        const row = [];
                        
                        // 销售明细部分
                        if (i < tableRows.length) {
                            row.push(...tableRows[i]);
                        } else {
                            row.push(...Array(tableColCount).fill(''));
                        }
                        
                        // 分隔列
                        row.push('');
                        row.push('');
                        
                        // 统计数据部分 (从索引1开始，因为0是标题)
                        if (i + 1 < statsData.length) {
                            row.push(...statsData[i + 1]);
                        } else {
                            row.push(...Array(statsColCount).fill(''));
                        }
                        
                        // 确保所有行的长度一致
                        while (row.length < totalColumns) row.push('');
                        
                        aoa.push(row);
                    }
                    
                    // 创建工作表
                    const worksheet = XLSX.utils.aoa_to_sheet(aoa);
                    
                    // 合并标题单元格
                    worksheet['!merges'] = [
                        // 标题行合并
                        {s: {r: 0, c: 0}, e: {r: 0, c: totalColumns - 1}}
                    ];
                    
                    // 设置列宽
                    const colWidths = [];
                    
                    // 销售明细部分列宽
                    for (let i = 0; i < tableColCount; i++) {
                        colWidths.push({wch: 15});
                    }
                    
                    // 分隔列
                    colWidths.push({wch: 3});
                    colWidths.push({wch: 3});
                    
                    // 统计数据部分列宽
                    for (let i = 0; i < statsColCount; i++) {
                        colWidths.push({wch: 12});
                    }
                    
                    worksheet['!cols'] = colWidths;
                    
                    // 添加工作表到工作簿
                    XLSX.utils.book_append_sheet(wb, worksheet, currentFileName + " 数据");
                    
                    // 下载Excel文件
                    const fileName = currentFileName + '_数据_' + new Date().toISOString().split('T')[0] + '.xlsx';
                    XLSX.writeFile(wb, fileName);
                    
                    alert('Excel文件导出成功!');
                } catch (error) {
                    console.error('导出Excel失败:', error);
                    alert('导出Excel失败: ' + error.message);
                }
            }
        });
    </script>
</body>
</html>
'''

def parse_time(time_str):
    return InventoryProcessor.parse_time(time_str)

def read_file(file_path):
    if not os.path.isfile(file_path):
        # 如果文件不存在，尝试在当前目录查找
        current_dir = os.getcwd()
        base_filename = os.path.basename(file_path)
        alternative_path = os.path.join(current_dir, base_filename)

        if os.path.isfile(alternative_path):
            file_path = alternative_path
        else:
            logging.warning(f"文件不存在: {file_path}")
            return []
    try:
        file_mtime = os.path.getmtime(file_path)
        cache_key = (file_path, file_mtime)

        # 使用缓存数据，如果存在
        if cache_key in read_file.cache:
            return read_file.cache[cache_key]

        with open(file_path, 'r') as file:
            lines = [line.strip() for line in file if line.strip()]

            # 优化排序 - 只在必要时进行排序
            if lines and ',' in lines[0]:  # 确保文件格式正确
                lines.sort(key=lambda x: parse_time(x.split(',')[0]))

            # 维护缓存大小，防止内存泄漏
            if len(read_file.cache) > 20:  # 限制缓存条目数量
                # 删除最旧的缓存项
                oldest_key = next(iter(read_file.cache))
                del read_file.cache[oldest_key]

            read_file.cache[cache_key] = lines
            return lines
    except Exception as e:
        logging.error(f"文件读取失败: {e}")
        return []

# 初始化缓存字典
read_file.cache = {}

# 添加结果缓存字典
process_request_cache = {}

def process_request(buttons, default_file, highlight_num_override=None, start=0, end=None):
    # 检测是否为AJAX请求
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    # 构建缓存键
    cache_key = (
        tuple(buttons),
        default_file,
        highlight_num_override,
        start,
        end,
        request.form.get('threshold'),
        request.form.get('highlight_num'),
        request.form.get('cut_value'),
        is_ajax  # 添加AJAX状态到缓存键
    )

    # 检查是否有表单提交操作，这会导致状态变化
    form_submitted = False
    for key in buttons:
        if key in request.form:
            form_submitted = True
            break

    # 检查是否有表单设置提交
    settings_submitted = 'threshold' in request.form or 'highlight_num' in request.form or 'cut_value' in request.form

    # 如果没有表单提交且缓存中有结果，直接返回缓存结果
    if not form_submitted and not settings_submitted and cache_key in process_request_cache:
        return process_request_cache[cache_key]

    selected_button = None
    for key in buttons:
        if key in request.form:
            selected_button = key
            break

    # 获取当前可用的文件
    file_paths = get_available_files()

    # 只有在点击按钮时更改当前文件
    if selected_button and selected_button in file_paths:
        session['current_file'] = file_paths[selected_button]
    # 如果是首次访问该路由，设置默认文件
    elif 'current_file' not in session:
        session['current_file'] = default_file
    # 否则保持当前文件不变

    file_to_show = session['current_file']
    data = read_file(file_to_show)
    is_ph = 'ph' in file_to_show.lower()
    is_ma = any(kw in file_to_show.lower() for kw in ['ma', 'mark'])
    is_moon ='moon' in file_to_show.lower()
    # 获取当前显示的文件名
    file_name = next((k for k, v in file_paths.items() if v == file_to_show), '')
    # 如果在URL中没有找到匹配的键，使用文件路径的基本名称
    if not file_name:
        file_name = os.path.basename(file_to_show).split('.')[0]

    # 获取文件配置
    file_config = config_manager.get_file_config(file_name)

    # 处理阈值
    try:
        threshold = int(request.form.get('threshold')) if request.form.get('threshold') else file_config['threshold']
    except:
        threshold = file_config['threshold']

    # 处理高光数量
    if highlight_num_override is not None:
        highlight_num = highlight_num_override
    elif request.form.get('highlight_num'):
        try:
            highlight_num = int(request.form['highlight_num'])
        except ValueError:
            highlight_num = file_config['highlight_num']
    else:
        highlight_num = file_config['highlight_num']

    # 使用配置中的文件类型标识
    is_ph = file_config.get('is_ph_file', 'ph' in file_to_show.lower())
    is_ma = file_config.get('is_ma', any(kw in file_to_show.lower() for kw in ['ma', 'mark']))

    processor = InventoryProcessor(
        threshold=threshold,
        highlight_num=highlight_num,
        is_ph_file=is_ph,
        is_ma=is_ma
    )

    processed, highlighted, total, kms_total = processor.process_data(data)

    # 处理特定文件的总销量调整
    if file_name == '签名应募':
        total += 419  # photo总销量加上1880
    elif file_name == '合影签售':
        total += 1208  # sign总销量加上4096
    elif file_name == 'coffee':
        total += 413   # jack总销量加上269
    elif file_name == 'kissoflife':
        total += 187   # nexz总销量加上319
    elif file_name == 'le':
        total += 483
    highlighted = highlighted if highlighted else []
    stats = InventoryProcessor.calculate_stats(highlighted)

    # Initialize all stats variables as None
    stats_176_275 = None
    stats_1_25 = None
    stats_26_40 = None
    stats_41_50 = None  # 新增41-50区间统计
    stats_1_50 = None
    stats_51_250 = None
    stats_51_200 = None
    stats_51_190 = None
    stats_51_150 = None
    stats_61_100 = None
    stats_101_140 = None
    stats_101_150 = None
    stats_61_140 = None
    stats_141_240 = None
    stats_26_50 = None
    stats_1_5 = None
    stats_1_15 = None
    stats_16_30 = None
    stats_160_220 = None
    stats_51_100 = None
    stats_1_20 = None
    stats_21_30 = None
    stats_151_250 = None
    stats_51_125 = None
    stats_126_200 = None
    stats_26_35 = None
    stats_1_6 = None  # 新增1-6区间统计
    stats_7_12 = None  # 新增7-12区间统计
    # 新增区间统计
    stats_1_125 = None  # wayv 1-125
    stats_126_225 = None  # wayv 126-225
    stats_6_10 = None  # soyeon 6-10
    stats_1_60 = None  # superjunior 1-60
    stats_61_260 = None  # superjunior 61-260
    stats_1_10 = None  # cravity 1-10
    stats_15_20 = None  # cravity 15-20

    # 动态计算显示阶段统计
    stage_stats = {}
    display_stages = file_config.get('display_stages', [{'name': '全部', 'start': 1, 'end': -1}])

    for stage in display_stages:
        start_idx = max(0, stage['start'] - 1)  # 转换为0基索引
        end_idx = len(highlighted) if stage['end'] == -1 else min(len(highlighted), stage['end'])

        if start_idx < len(highlighted):
            stage_values = highlighted[start_idx:end_idx]
            stage_stats[stage['name']] = {
                'values': stage_values,
                'stats': InventoryProcessor.calculate_stats(stage_values),
                'start': stage['start'],
                'end': stage['end']
            }

    # 为了兼容现有模板，保留一些常用的统计变量
    stats_1_25 = stage_stats.get('1-25', {}).get('stats')
    stats_26_40 = stage_stats.get('26-40', {}).get('stats')
    stats_1_50 = stage_stats.get('1-50', {}).get('stats')
    stats_51_200 = stage_stats.get('51-200', {}).get('stats')

    # 为了兼容现有模板，设置一些默认的统计变量
    stats_176_275 = stats_1_5 = stats_6_10 = stats_1_30 = stats_31_100 = None
    stats_51_250 = stats_160_220 = stats_1_15 = stats_16_30 = stats_1_20 = None
    stats_21_30 = stats_151_250 = stats_51_125 = stats_126_200 = stats_26_35 = None
    stats_1_6 = stats_7_12 = stats_1_125 = stats_126_225 = stats_61_100 = None
    stats_101_140 = stats_61_140 = stats_141_240 = stats_26_50 = stats_51_100 = None
    stats_51_190 = stats_51_150 = stats_1_60 = stats_61_260 = stats_1_10 = None
    stats_15_20 = stats_41_50 = None
        stats_1_10 = InventoryProcessor.calculate_stats(values_1_10)
        values_15_20 = highlighted[5:10]
        stats_15_20 = InventoryProcessor.calculate_stats(values_15_20)
    cut = None
    if 'cut_value' in request.form:
        try:
            divisor = float(request.form['cut_value'])
            cut = total / divisor if divisor != 0 else "除零错误"
        except:
            cut = "无效输入"

    # 准备渲染结果
    result = render_template_string(template,
                                    show_buttons=True,
                                    buttons=buttons,
                                    data=processed,
                                    highlighted_values=highlighted,
                                    stats=stats,
                                    total=int(total),
                                    kms_total=int(kms_total),
                                    highlight_num=highlight_num,
                                    cut=cut,
                                    file_name=file_name,
                                    notification=file_config['notification'],
                                    stage_stats=stage_stats,
                                    display_stages=display_stages,
                                    stats_176_275=stats_176_275,
                                    stats_1_25=stats_1_25,
                                    stats_26_40=stats_26_40,
                                    stats_41_50=stats_41_50,  # 添加新的统计变量
                                    stats_1_50=stats_1_50,
                                    stats_51_250=stats_51_250,
                                    stats_51_200=stats_51_200,
                                    stats_51_190=stats_51_190,
                                    stats_51_150=stats_51_150,
                                    stats_61_100=stats_61_100,
                                    stats_101_140=stats_101_140,
                                    stats_61_140=stats_61_140,
                                    stats_141_240=stats_141_240,
                                    stats_26_50=stats_26_50,
                                    stats_51_100=stats_51_100,
                                    stats_160_220=stats_160_220,
                                    stats_1_5=stats_1_5,
                                    stats_1_15=stats_1_15,
                                    stats_16_30=stats_16_30,
                                    stats_1_20=stats_1_20,
                                    stats_21_30=stats_21_30,
                                    stats_151_250=stats_151_250,
                                    stats_51_125=stats_51_125,
                                    stats_126_200=stats_126_200,
                                    stats_26_35=stats_26_35,
                                    stats_1_6=stats_1_6,  # 添加新的统计变量
                                    stats_7_12=stats_7_12,  # 添加新的统计变量
                                    stats_1_125=stats_1_125,  # 添加新的统计变量
                                    stats_126_225=stats_126_225,  # 添加新的统计变量
                                    stats_6_10=stats_6_10,  # 添加新的统计变量
                                    stats_1_60=stats_1_60,  # 添加新的统计变量
                                    stats_61_260=stats_61_260,  # 添加新的统计变量
                                    stats_1_10=stats_1_10,
                                    stats_15_20=stats_15_20)  # 添加新的统计变量

    # 缓存结果，仅在表单提交时不缓存
    if not form_submitted:
        # 维护缓存大小，防止内存溢出
        if len(process_request_cache) > 30:  # 限制缓存条目数量
            # 删除一个随机键以避免同时删除所有缓存
            random_key = next(iter(process_request_cache))
            del process_request_cache[random_key]
        process_request_cache[cache_key] = result

    return result

@app.route('/tb02', methods=['GET', 'POST'])
def tb02_data():
    buttons = get_available_buttons()
    return process_request(buttons, 'sion.txt')
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=7799, debug=True)