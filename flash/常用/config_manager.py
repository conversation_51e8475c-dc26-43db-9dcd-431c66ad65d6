import json
import os
from typing import Dict, List, Optional, Any
import hashlib

class ConfigManager:
    """配置管理器 - 管理文件配置和URL设置"""
    
    def __init__(self, config_file='admin_config.json'):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        
        # 默认配置
        return {
            'admin_password_hash': self.hash_password('admin123'),  # 默认密码
            'file_configs': {},
            'url_configs': {},
            'global_settings': {
                'default_threshold': 190,
                'default_highlight_num': 40,
                'default_notification': '🐾 圆梦大使签售表格 🐾'
            }
        }
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    @staticmethod
    def hash_password(password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str) -> bool:
        """验证密码"""
        return self.hash_password(password) == self.config.get('admin_password_hash', '')
    
    def change_password(self, new_password: str) -> bool:
        """修改管理员密码"""
        self.config['admin_password_hash'] = self.hash_password(new_password)
        return self.save_config()
    
    def get_file_config(self, file_name: str) -> Dict[str, Any]:
        """获取文件配置"""
        return self.config['file_configs'].get(file_name, {
            'threshold': self.config['global_settings']['default_threshold'],
            'highlight_num': self.config['global_settings']['default_highlight_num'],
            'display_stages': [{'name': '全部', 'start': 1, 'end': -1}],
            'notification': self.config['global_settings']['default_notification'],
            'is_ph_file': False,
            'is_ma': False
        })
    
    def set_file_config(self, file_name: str, config: Dict[str, Any]) -> bool:
        """设置文件配置"""
        self.config['file_configs'][file_name] = config
        return self.save_config()
    
    def get_url_config(self, url_path: str) -> Dict[str, Any]:
        """获取URL配置"""
        return self.config['url_configs'].get(url_path, {})
    
    def set_url_config(self, url_path: str, config: Dict[str, Any]) -> bool:
        """设置URL配置"""
        self.config['url_configs'][url_path] = config
        return self.save_config()
    
    def delete_url_config(self, url_path: str) -> bool:
        """删除URL配置"""
        if url_path in self.config['url_configs']:
            del self.config['url_configs'][url_path]
            return self.save_config()
        return False
    
    def get_all_file_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有文件配置"""
        return self.config['file_configs']
    
    def get_all_url_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有URL配置"""
        return self.config['url_configs']
    
    def get_available_files(self) -> List[str]:
        """获取可用的txt文件列表"""
        files = []
        current_dir = os.getcwd()
        for file in os.listdir(current_dir):
            if file.endswith('.txt'):
                files.append(os.path.splitext(file)[0])
        return sorted(files)
    
    def get_global_settings(self) -> Dict[str, Any]:
        """获取全局设置"""
        return self.config['global_settings']
    
    def set_global_settings(self, settings: Dict[str, Any]) -> bool:
        """设置全局设置"""
        self.config['global_settings'].update(settings)
        return self.save_config()

# 全局配置管理器实例
config_manager = ConfigManager()
