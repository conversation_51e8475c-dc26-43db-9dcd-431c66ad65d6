# 🛠️ 管理员系统使用说明

## 📋 系统概述

管理员系统允许您通过Web界面动态配置文件参数和创建自定义URL，无需修改代码即可管理不同文件的显示设置。

## 🔐 登录管理员系统

1. 访问 `/admin/login` 页面
2. 输入管理员密码（默认：`admin123`）
3. 登录成功后进入管理员控制台

## 📁 文件配置管理

### 配置文件参数

在"文件配置"标签页中，您可以为每个txt文件设置：

- **阈值**：数据过滤的阈值（如190、270等）
- **高光个数**：显示的高光数据数量
- **通知内容**：页面顶部显示的标题
- **文件类型**：
  - PH文件：勾选后使用PH文件的特殊处理逻辑
  - MA文件：勾选后使用MA文件的特殊处理逻辑
- **显示阶段**：定义数据分段显示
  - 阶段名称：如"1-15"、"16-30"
  - 开始位置：从第几个数据开始
  - 结束位置：到第几个数据结束（-1表示到末尾）

### 示例配置

```json
{
  "threshold": 190,
  "highlight_num": 30,
  "display_stages": [
    {"name": "1-15", "start": 1, "end": 15},
    {"name": "16-30", "start": 16, "end": 30}
  ],
  "notification": "🐾 圆梦大使签售表格 - YY 🐾",
  "is_ph_file": false,
  "is_ma": false
}
```

## 🔗 URL管理

### 创建自定义URL

在"URL管理"标签页中，您可以：

1. **创建URL路径**：如 `/nct`、`/ph`、`/wayv`
2. **关联文件**：选择该URL可以访问的txt文件（支持多选）
3. **设置默认文件**：用户首次访问时显示的文件

### URL配置示例

- `/nct` → 关联文件：yy, kun, xj, ten, hen → 默认：yy
- `/ph` → 关联文件：phyy, phkun, phxj → 默认：phyy
- `/wayv` → 关联文件：wayv → 默认：wayv

## ⚙️ 全局设置

### 默认参数设置

- **默认阈值**：新文件的默认阈值
- **默认高光个数**：新文件的默认高光数量
- **默认通知内容**：新文件的默认标题

### 密码管理

在全局设置中可以修改管理员密码：
1. 输入新密码
2. 确认新密码
3. 点击"修改密码"

## 🚀 系统优势

### 代码简化

- **删除硬编码**：移除了数百行的硬编码文件配置
- **动态配置**：所有配置通过JSON文件管理
- **统一模板**：使用单一模板处理所有文件类型

### 管理便捷

- **可视化配置**：通过Web界面直观配置
- **实时生效**：配置修改后立即生效
- **批量管理**：可以同时管理多个文件和URL

### 功能扩展

- **灵活阶段设置**：可以为不同文件设置不同的显示阶段
- **多URL支持**：一个文件可以在多个URL中使用
- **权限控制**：通过密码保护管理功能

## 📝 配置文件格式

配置文件 `admin_config.json` 包含三个主要部分：

1. **file_configs**：文件配置
2. **url_configs**：URL配置  
3. **global_settings**：全局设置

## 🔧 使用流程

1. **登录管理员系统**
2. **配置文件参数**：设置阈值、高光数、显示阶段等
3. **创建URL**：为文件组合创建访问路径
4. **测试访问**：访问创建的URL验证配置
5. **调整优化**：根据需要调整配置参数

## 💡 最佳实践

- **备份配置**：定期备份 `admin_config.json` 文件
- **测试配置**：修改配置后及时测试效果
- **命名规范**：使用清晰的阶段名称和URL路径
- **权限管理**：定期更换管理员密码

## 🆘 常见问题

**Q: 配置修改后没有生效？**
A: 检查JSON格式是否正确，重启应用程序

**Q: 忘记管理员密码？**
A: 删除配置文件，系统会重置为默认密码 `admin123`

**Q: 如何批量配置多个文件？**
A: 可以直接编辑 `admin_config.json` 文件，然后重启应用

**Q: URL冲突怎么办？**
A: 系统会提示URL已存在，请使用不同的路径名称
